#!/usr/bin/env python3
import os
import cv2
import numpy as np
import sqlite3
from pathlib import Path
import json
import struct
from datetime import datetime
import rosbag2_py
from rclpy.serialization import deserialize_message
from rosidl_runtime_py.utilities import get_message
import sensor_msgs.msg
import geometry_msgs.msg
import glob
import sys
import argparse

class RosbagParser:
    def __init__(self, bag_path):
        self.bag_path = Path(bag_path)
        self.output_dir = self.bag_path / "parsed_data"
        self.setup_directories()
        
        # 用于存储所有数据的列表
        self.imu_data_list = []
        self.robot_state_list = []
    
    def setup_directories(self):
        """创建输出目录结构"""
        dirs = [
            "robot_state",           # 关节状态
            "images/rgb",            # RGB图像
            "images/depth",          # 深度图像
            "imu",                   # IMU数据
            "lidar",                 # 点云数据
            "metadata",              # 元数据根目录
            "metadata/images/rgb",   # RGB图像元数据
            "metadata/images/depth", # 深度图像元数
            "metadata/lidar"         # 点云元数据
        ]
        
        for dir_name in dirs:
            (self.output_dir / dir_name).mkdir(parents=True, exist_ok=True)
    
    def ros_image_to_cv2(self, ros_image):
        """将ROS图像消息转换为OpenCV格式"""
        if ros_image.encoding == "bgr8":
            cv_image = np.frombuffer(ros_image.data, dtype=np.uint8).reshape(
                ros_image.height, ros_image.width, 3)
        elif ros_image.encoding == "rgb8":
            cv_image = np.frombuffer(ros_image.data, dtype=np.uint8).reshape(
                ros_image.height, ros_image.width, 3)
            cv_image = cv2.cvtColor(cv_image, cv2.COLOR_RGB2BGR)
        elif ros_image.encoding == "mono8":
            cv_image = np.frombuffer(ros_image.data, dtype=np.uint8).reshape(
                ros_image.height, ros_image.width)
        elif ros_image.encoding == "16UC1":
            cv_image = np.frombuffer(ros_image.data, dtype=np.uint16).reshape(
                ros_image.height, ros_image.width)
        else:
            raise ValueError(f"不支持的图像编码: {ros_image.encoding}")
        
        return cv_image

    def pointcloud2_to_array(self, cloud_msg):
        """将PointCloud2消息转换为numpy数组"""
        points = []
        for i in range(0, len(cloud_msg.data), cloud_msg.point_step):
            point_data = cloud_msg.data[i:i+cloud_msg.point_step]
            
            # 解析XYZ坐标
            x = struct.unpack('f', point_data[0:4])[0]
            y = struct.unpack('f', point_data[4:8])[0]
            z = struct.unpack('f', point_data[8:12])[0]
            
            # 如果有强度信息
            intensity = 0
            if len(point_data) >= 16:
                intensity = struct.unpack('f', point_data[12:16])[0]
            
            points.append([x, y, z, intensity])
        
        return np.array(points)

    def save_pcd(self, points, filename):
        """保存点云为PCD格式"""
        with open(filename, 'w') as f:
            f.write("# .PCD v0.7 - Point Cloud Data file format\n")
            f.write("VERSION 0.7\n")
            f.write("FIELDS x y z intensity\n")
            f.write("SIZE 4 4 4 4\n")
            f.write("TYPE F F F F\n")
            f.write("COUNT 1 1 1 1\n")
            f.write(f"WIDTH {len(points)}\n")
            f.write("HEIGHT 1\n")
            f.write("VIEWPOINT 0 0 0 1 0 0 0\n")
            f.write(f"POINTS {len(points)}\n")
            f.write("DATA ascii\n")
            
            for point in points:
                f.write(f"{point[0]} {point[1]} {point[2]} {point[3]}\n")

    def save_summary(self, counters):
        """保存解析摘要"""
        summary = {
            "parse_time": datetime.now().isoformat(),
            "total_messages": sum(counters.values()),
            "topics": counters
        }
        
        filename = self.output_dir / "metadata" / "parse_summary.json"
        with open(filename, 'w') as f:
            json.dump(summary, f, indent=2)

    def parse_bag(self):
        """解析ROS2 bag文件"""
        storage_options = rosbag2_py.StorageOptions(
            uri=str(self.bag_path),
            storage_id='sqlite3'
        )
        
        converter_options = rosbag2_py.ConverterOptions(
            input_serialization_format='cdr',
            output_serialization_format='cdr'
        )
        
        reader = rosbag2_py.SequentialReader()
        reader.open(storage_options, converter_options)
        
        topic_types = reader.get_all_topics_and_types()
        type_map = {topic.name: topic.type for topic in topic_types}
        
        counters = {topic: 0 for topic in type_map.keys()}
        
        print(f"发现的话题: {list(type_map.keys())}")
        
        while reader.has_next():
            (topic, data, timestamp) = reader.read_next()
            
            if topic in type_map:
                msg_type = get_message(type_map[topic])
                msg = deserialize_message(data, msg_type)
                
                self.process_message(topic, msg, timestamp, counters[topic])
                counters[topic] += 1
                
                if counters[topic] % 100 == 0:
                    print(f"已处理 {topic}: {counters[topic]} 条消息")
        
        self.save_summary(counters)
        self.save_combined_data()
        print(f"解析完成！数据保存在: {self.output_dir}")
        
        # 生成视频
        self.create_videos()
    
    def process_message(self, topic, msg, timestamp, counter):
        """处理不同类型的消息"""
        if topic == "/lowstate":
            self.save_robot_state(msg, timestamp, counter)
        elif topic == "/color/image_raw":
            self.save_rgb_image(msg, timestamp, counter)
        elif topic == "/depth/image_rect_raw":
            self.save_depth_image(msg, timestamp, counter)
        elif topic == "/livox/imu":
            self.save_imu_data(msg, timestamp, counter)
        elif topic == "/livox/lidar":
            self.save_lidar_data(msg, timestamp, counter)
    
    def save_robot_state(self, msg, timestamp, counter):
        """保存机器人状态数据到内存列表"""
        # 提取电机状态数据
        motor_data = []
        for i, motor in enumerate(msg.motor_state):
            motor_info = {
                "motor_id": i,
                "mode": int(motor.mode),
                "position": float(motor.q),
                "velocity": float(motor.dq),
                "acceleration": float(motor.ddq),
                "torque_est": float(motor.tau_est),
                "temperature": [int(motor.temperature[0]), int(motor.temperature[1])],
                "voltage": float(motor.vol),
                "sensor": [int(motor.sensor[0]), int(motor.sensor[1])],
                "motor_state": int(motor.motorstate)
            }
            motor_data.append(motor_info)
        
        data = {
            "timestamp": int(timestamp),
            "counter": counter,
            "version": [int(msg.version[0]), int(msg.version[1])],
            "mode_pr": int(msg.mode_pr),
            "mode_machine": int(msg.mode_machine),
            "tick": int(msg.tick),
            "imu_state": {
                "quaternion": [float(msg.imu_state.quaternion[i]) for i in range(4)],
                "gyroscope": [float(msg.imu_state.gyroscope[i]) for i in range(3)],
                "accelerometer": [float(msg.imu_state.accelerometer[i]) for i in range(3)],
                "rpy": [float(msg.imu_state.rpy[i]) for i in range(3)],
                "temperature": int(msg.imu_state.temperature)
            },
            "motor_states": motor_data,
            "wireless_remote": [int(x) for x in msg.wireless_remote],
            "reserve": [int(x) for x in msg.reserve],
            "crc": int(msg.crc)
        }
        
        self.robot_state_list.append(data)
    
    def save_rgb_image(self, msg, timestamp, counter):
        """保存RGB图像"""
        cv_image = self.ros_image_to_cv2(msg)
        filename = self.output_dir / "images/rgb" / f"rgb_{counter:06d}.jpg"
        cv2.imwrite(str(filename), cv_image)
        
        # 保存图像元数据到metadata文件夹
        metadata = {
            "timestamp": int(timestamp),
            "width": int(msg.width),
            "height": int(msg.height),
            "encoding": str(msg.encoding)
        }
        meta_file = self.output_dir / "metadata/images/rgb" / f"rgb_{counter:06d}_meta.json"
        with open(meta_file, 'w') as f:
            json.dump(metadata, f, indent=2)
    
    def save_depth_image(self, msg, timestamp, counter):
        """保存深度图像"""
        cv_image = self.ros_image_to_cv2(msg)
        filename = self.output_dir / "images/depth" / f"depth_{counter:06d}.png"
        cv2.imwrite(str(filename), cv_image)
        
        metadata = {
            "timestamp": int(timestamp),
            "width": int(msg.width),
            "height": int(msg.height),
            "encoding": str(msg.encoding)
        }
        meta_file = self.output_dir / "metadata/images/depth" / f"depth_{counter:06d}_meta.json"
        with open(meta_file, 'w') as f:
            json.dump(metadata, f, indent=2)
    
    def save_imu_data(self, msg, timestamp, counter):
        """保存IMU数据到内存列表"""
        data = {
            "timestamp": int(timestamp),
            "counter": counter,
            "orientation": {
                "x": float(msg.orientation.x),
                "y": float(msg.orientation.y),
                "z": float(msg.orientation.z),
                "w": float(msg.orientation.w)
            },
            "angular_velocity": {
                "x": float(msg.angular_velocity.x),
                "y": float(msg.angular_velocity.y),
                "z": float(msg.angular_velocity.z)
            },
            "linear_acceleration": {
                "x": float(msg.linear_acceleration.x),
                "y": float(msg.linear_acceleration.y),
                "z": float(msg.linear_acceleration.z)
            }
        }
        
        self.imu_data_list.append(data)
    
    def save_lidar_data(self, msg, timestamp, counter):
        """保存点云数据和元数据"""
        # 保存为PCD格式
        points = self.pointcloud2_to_array(msg)
        filename = self.output_dir / "lidar" / f"lidar_{counter:06d}.pcd"
        self.save_pcd(points, filename)
        
        # 保存元数据到单独文件
        metadata = {
            "timestamp": int(timestamp),
            "counter": counter,
            "width": int(msg.width),
            "height": int(msg.height),
            "point_count": int(msg.width * msg.height),
            "fields": [{"name": str(field.name), "offset": int(field.offset), 
                       "datatype": int(field.datatype), "count": int(field.count)} 
                      for field in msg.fields]
        }
        meta_file = self.output_dir / "metadata/lidar" / f"lidar_{counter:06d}_meta.json"
        with open(meta_file, 'w') as f:
            json.dump(metadata, f, indent=2)

    def create_videos(self):
        """将RGB和深度图像序列转换为MP4视频"""
        print("开始生成视频...")
        
        # 生成RGB视频
        rgb_dir = self.output_dir / "images/rgb"
        rgb_video_path = rgb_dir / "rgb_video.mp4"
        self._create_video_from_images(rgb_dir, rgb_video_path, "rgb_*.jpg")
        
        # 生成深度视频
        depth_dir = self.output_dir / "images/depth"
        depth_video_path = depth_dir / "depth_video.mp4"
        self._create_video_from_images(depth_dir, depth_video_path, "depth_*.png")
        
        print(f"视频生成完成！")
        print(f"RGB视频: {rgb_video_path}")
        print(f"深度视频: {depth_video_path}")

    def save_combined_data(self):
        """将所有数据保存为合并的JSON文件"""
        print("正在保存合并的数据文件...")
        
        # 保存合并的IMU数据
        if self.imu_data_list:
            imu_file = self.output_dir / "imu" / "imu_data.json"
            with open(imu_file, 'w') as f:
                json.dump(self.imu_data_list, f, indent=2)
            print(f"IMU数据已保存: {len(self.imu_data_list)} 条记录")
        
        # 保存合并的机器人状态数据
        if self.robot_state_list:
            robot_file = self.output_dir / "robot_state" / "robot_states.json"
            with open(robot_file, 'w') as f:
                json.dump(self.robot_state_list, f, indent=2)
            print(f"机器人状态数据已保存: {len(self.robot_state_list)} 条记录")

    def _create_video_from_images(self, image_dir, output_path, pattern):
        """从图像序列创建视频"""
        # 获取所有图像文件并排序
        image_files = sorted(glob.glob(str(image_dir / pattern)))
        
        if not image_files:
            print(f"警告: 在 {image_dir} 中未找到匹配 {pattern} 的图像文件")
            return
        
        # 读取第一张图像获取尺寸
        first_image = cv2.imread(image_files[0])
        if first_image is None:
            print(f"错误: 无法读取图像 {image_files[0]}")
            return
        
        height, width = first_image.shape[:2]
        
        # 设置视频编码器和参数
        fourcc = cv2.VideoWriter_fourcc(*'mp4v')
        fps = 30.0  # 可以根据需要调整帧率
        
        # 创建视频写入器
        video_writer = cv2.VideoWriter(str(output_path), fourcc, fps, (width, height))
        
        if not video_writer.isOpened():
            print(f"错误: 无法创建视频文件 {output_path}")
            return
        
        print(f"正在处理 {len(image_files)} 张图像...")
        
        # 逐帧写入视频
        for i, image_file in enumerate(image_files):
            image = cv2.imread(image_file)
            if image is not None:
                # 确保图像尺寸一致
                if image.shape[:2] != (height, width):
                    image = cv2.resize(image, (width, height))
                video_writer.write(image)
            
            if (i + 1) % 100 == 0:
                print(f"已处理 {i + 1}/{len(image_files)} 张图像")
        
        # 释放资源
        video_writer.release()
        print(f"视频保存完成: {output_path}")

def main():
    """主函数，支持命令行参数"""
    parser = argparse.ArgumentParser(description='解析ROS2 bag文件')
    parser.add_argument('bag_path', nargs='?',
                       default="/home/<USER>/ros2_ws/sensor_data_20250731_110626",
                       help='要解析的bag文件路径')

    args = parser.parse_args()

    # 检查bag路径是否存在
    if not os.path.exists(args.bag_path):
        print(f"错误: bag文件路径不存在: {args.bag_path}")
        sys.exit(1)

    print(f"开始解析bag文件: {args.bag_path}")

    # 创建解析器并执行解析
    bag_parser = RosbagParser(args.bag_path)
    bag_parser.parse_bag()

if __name__ == "__main__":
    main()
