#!/usr/bin/env python3
import subprocess
import os
import sys
from datetime import datetime
from pathlib import Path

def main():
    # 创建保存目录
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    bag_name = f"sensor_data_{timestamp}"

    # 录制所有传感器话题
    topics = [
        "/livox/lidar",
        "/livox/imu",
        "/lowstate",
        "/color/image_raw",
        "/depth/image_rect_raw"
    ]

    cmd = ["ros2", "bag", "record"] + topics + ["-o", bag_name]

    print(f"开始录制传感器数据到 {bag_name}")
    print("按 Ctrl+C 停止录制")

    try:
        subprocess.run(cmd)
    except KeyboardInterrupt:
        print(f"\n录制完成，数据保存在 {bag_name}")

        # 自动调用解析脚本
        auto_parse_bag(bag_name)

def auto_parse_bag(bag_path):
    """自动调用解析脚本处理刚录制的bag文件"""
    # 获取当前脚本所在目录
    current_dir = Path(__file__).parent
    parse_script = current_dir / "parse_rosbag.py"

    # 检查解析脚本是否存在
    if not parse_script.exists():
        print(f"警告: 解析脚本不存在: {parse_script}")
        return

    # 检查bag文件是否存在
    if not os.path.exists(bag_path):
        print(f"警告: bag文件不存在: {bag_path}")
        return

    print(f"\n开始自动解析bag文件: {bag_path}")
    print("=" * 50)

    try:
        # 调用解析脚本
        subprocess.run([
            sys.executable, str(parse_script), bag_path
        ], check=True)

        print("=" * 50)
        print("✅ bag文件解析完成！")

    except subprocess.CalledProcessError as e:
        print(f"❌ 解析过程中出现错误: {e}")
        print("请手动运行解析脚本:")
        print(f"python3 {parse_script} {bag_path}")
    except Exception as e:
        print(f"❌ 调用解析脚本时出现错误: {e}")
        print("请手动运行解析脚本:")
        print(f"python3 {parse_script} {bag_path}")

if __name__ == '__main__':
    main()